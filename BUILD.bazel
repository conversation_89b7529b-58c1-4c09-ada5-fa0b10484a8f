load("@gazelle//:def.bzl", "gazelle")
load("@rules_java//java:defs.bzl", "java_library", "java_plugin")
load("@protobuf//bazel:proto_library.bzl", "proto_library")
load("@rules_go//go:def.bzl", "go_library")

# Lombok annotation processor and compile-only wrapper
java_library(
    name = "lombok_lib",
    exports = ["@maven//:org_projectlombok_lombok"],
    neverlink = True,
    visibility = ["//visibility:public"],
)

java_library(
    name = "mapstruct_lib",
    exports = ["@maven//:org_mapstruct_mapstruct"],
    visibility = ["//visibility:public"],
)

java_plugin(
    name = "lombok_ap",
    processor_class = "lombok.launch.AnnotationProcessorHider$AnnotationProcessor",
    deps = ["@maven//:org_projectlombok_lombok", "@maven//:org_projectlombok_lombok_mapstruct_binding"],
    visibility = ["//visibility:public"],
)

java_plugin(
    name = "mapstruct_ap",
    processor_class = "org.mapstruct.ap.MappingProcessor",
    deps = ["@maven//:org_mapstruct_mapstruct_processor"],
    visibility = ["//visibility:public"],
)

# Mapstruct protobuf SPI implementation
java_plugin(
    name = "mapstruct_spi_protobuf_ap",
    processor_class = "no.entur.mapstruct.spi.protobuf.ProcessingEnvOptionsHolder",
    deps = ["@maven//:no_entur_mapstruct_spi_protobuf_spi_impl"],
    visibility = ["//visibility:public"],
)

# Protobuf well-known types

proto_library(
    name = "proto_wkt",
    deps = [
        "@protobuf//:any_proto",
        "@protobuf//:api_proto",
        "@protobuf//:compiler_plugin_proto",
        "@protobuf//:descriptor_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:empty_proto",
        "@protobuf//:field_mask_proto",
        "@protobuf//:source_context_proto",
        "@protobuf//:struct_proto",
        "@protobuf//:timestamp_proto",
        "@protobuf//:type_proto",
        "@protobuf//:wrappers_proto",
    ],
    visibility = ["//visibility:public"],
)

proto_library(
    name = "proto_googleapis",
    deps = [
        ":proto_wkt",

        "@googleapis//google/type:calendar_period_proto",
        "@googleapis//google/type:color_proto",
        "@googleapis//google/type:date_proto",
        "@googleapis//google/type:datetime_proto",
        "@googleapis//google/type:dayofweek_proto",
        "@googleapis//google/type:decimal_proto",
        "@googleapis//google/type:expr_proto",
        "@googleapis//google/type:fraction_proto",
        "@googleapis//google/type:interval_proto",
        "@googleapis//google/type:latlng_proto",
        "@googleapis//google/type:localized_text_proto",
        "@googleapis//google/type:money_proto",
        "@googleapis//google/type:month_proto",
        "@googleapis//google/type:phone_number_proto",
        "@googleapis//google/type:postal_address_proto",
        "@googleapis//google/type:quaternion_proto",
        "@googleapis//google/type:timeofday_proto",

        "@googleapis//google/rpc:code_proto",
        "@googleapis//google/rpc:error_details_proto",
        "@googleapis//google/rpc:status_proto",
    ],
    visibility = ["//visibility:public"],
)

go_library(
    name = "proto_wkt_go",
    deps = [
        "@org_golang_google_protobuf//types/known/anypb",
        "@org_golang_google_protobuf//types/known/apipb",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_google_protobuf//types/known/fieldmaskpb",
        "@org_golang_google_protobuf//types/known/sourcecontextpb",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_google_protobuf//types/known/typepb",
        "@org_golang_google_protobuf//types/known/wrapperspb",
    ],
    visibility = ["//visibility:public"],
)

go_library(
    name = "proto_googleapis_go",
    deps = [
        ":proto_wkt_go",

        "@googleapis//google/type:calendar_period_go_proto",
        "@googleapis//google/type:color_go_proto",
        "@googleapis//google/type:date_go_proto",
        "@googleapis//google/type:datetime_go_proto",
        "@googleapis//google/type:dayofweek_go_proto",
        "@googleapis//google/type:decimal_go_proto",
        "@googleapis//google/type:expr_go_proto",
        "@googleapis//google/type:fraction_go_proto",
        "@googleapis//google/type:interval_go_proto",
        "@googleapis//google/type:latlng_go_proto",
        "@googleapis//google/type:localized_text_go_proto",
        "@googleapis//google/type:money_go_proto",
        "@googleapis//google/type:month_go_proto",
        "@googleapis//google/type:phone_number_go_proto",
        "@googleapis//google/type:postaladdress_go_proto",
        "@googleapis//google/type:quaternion_go_proto",
        "@googleapis//google/type:timeofday_go_proto",

        "@googleapis//google/rpc:code_go_proto",
        "@googleapis//google/rpc:errdetails_go_proto",
        "@googleapis//google/rpc:status_go_proto",
    ],
    visibility = ["//visibility:public"],
)

java_library(
    name = "proto_wkt_java",
    deps = [
        "@protobuf//:protobuf_java",
        "@protobuf//:protobuf_java_util",
    ],
    visibility = ["//visibility:public"],
)

java_library(
    name = "proto_googleapis_java",
    deps = [
        ":proto_wkt_java",

        "@googleapis//google/type:type_java_proto",
        "@googleapis//google/rpc:rpc_java_proto",
    ],
    visibility = ["//visibility:public"],
)
