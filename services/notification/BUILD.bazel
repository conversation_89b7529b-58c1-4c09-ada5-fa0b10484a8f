load("@rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_library(
    name = "lib",
    srcs = glob(["**/*.go"]),
    importpath = "github.com/example/bazel-example/services/notification",
    deps = [
        "//proto/notification:grpc_go",
    ],
)

go_binary(
    name = "app",
    embed = [":lib"],
    visibility = ["//visibility:public"],
)

# go_test(
#     name = "test",
# #     srcs = glob(["**/*.go"]),
#     embed = [":lib"],
# )
