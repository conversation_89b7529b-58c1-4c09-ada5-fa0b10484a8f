load("@protobuf//bazel:proto_library.bzl", "proto_library")
load("@protobuf//bazel:java_proto_library.bzl", "java_proto_library")
load("@rules_go//proto:def.bzl", "go_proto_library")
load("@rules_go//go:def.bzl", "go_library")
load("@rules_java//java:defs.bzl", "java_library")

proto_library(
    name = "proto",
    srcs = glob(["**/*.proto"]),
    deps = ["//:proto_googleapis"],
    strip_import_prefix = "/proto",
    visibility = ["//visibility:public"],
)

java_library(
    name = "proto_java",
    deps = [":gen_proto_java", "//:proto_googleapis_java"],
    visibility = ["//visibility:public"],
)

go_library(
    name = "proto_go",
    deps = [":gen_proto_go", "//:proto_googleapis_go"],
    visibility = ["//visibility:public"],
)

java_proto_library(
    name = "gen_proto_java",
    deps = [":proto"],
    visibility = ["//visibility:private"],
)

go_proto_library(
    name = "gen_proto_go",
    importpath = "github.com/example/bazel-example/proto/common",
    protos = [":proto"],
    visibility = ["//visibility:public"],
)
